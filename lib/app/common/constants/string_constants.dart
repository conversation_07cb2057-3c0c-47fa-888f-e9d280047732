/// String constants for the application
class AppStrings {
  /// Private constructor to prevent instantiation
  AppStrings._();

  // App name
  static const String appName = '<PERSON><PERSON><PERSON>';

  // Navigation
  static const String home = 'Home';
  static const String about = 'About';
  static const String skills = 'Skills';
  static const String projects = 'Projects';
  static const String career = 'Career';
  static const String contact = 'Contact';
  static const String education = 'Education';

  // Section titles
  static const String aboutMeTitle = 'About Me';
  static const String skillsTitle = 'Skills';
  static const String projectsTitle = 'Featured Projects';
  static const String careerTitle = 'Career Timeline';
  static const String educationTitle = 'Education';
  static const String contactTitle = "Let's Connect";

  // Section subtitles
  static const String aboutMeSubtitle = 'Get to know me better';
  static const String skillsSubtitle = 'What I bring to the table';
  static const String projectsSubtitle = 'Some of my recent work';
  static const String careerSubtitle = 'My professional journey';
  static const String educationSubtitle = 'My academic background';
  static const String contactSubtitle = 'Get in touch with me';

  // About section
  static const String aboutMeDescription =
      "I'm a passionate Flutter developer with 6+ years of experience in "
      'creating optimized, high-performance mobile applications. I specialize '
      'in cross-platform development, responsive UI design, and integrating '
      'with various backend services.';
  static const String aboutMePassion =
      "I'm passionate about creating apps that not only have exceptional UX but "
      'also solve real problems and deliver exceptional user experiences.';

  // Contact section
  static const String contactDescription =
      "Have a project in mind or just want to say hello? I'd love to hear "
      "from you! Let's discuss how we can work together to bring your ideas "
      'to life.';
  static const String emailMe = 'Email Me';
  static const String messageMe = 'Message Me';
  static const String downloadCV = 'Download CV';
  static const String sayHello = 'Say Hello';

  // Button labels
  static const String viewProject = 'View Project';
  static const String learnMore = 'Learn More';
  static const String sendMessage = 'Send Message';
  static const String viewAll = 'View All';

  // Error messages
  static const String errorLaunchingUrl = 'Could not launch URL';
  static const String errorLaunchingEmail = 'Could not launch email client';
  static const String errorLaunchingWhatsApp = 'Could not launch WhatsApp';

  // Success messages
  static const String copiedToClipboard = 'Copied to clipboard';

  // Placeholders
  static const String emailSubject = 'Contact from Portfolio Website';
  static const String cvFileName = 'Saksham_Srivastava_CV.pdf';

  // Mobile-specific messages
  static const String cvDownloadWebOnly =
      'CV download is only available on web platform';
}
