PODS:
  - Flutter (1.0.0)
  - flutter_native_splash (2.4.3):
    - Flutter
  - open_file_ios (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_native_splash: df59bb2e1421aa0282cb2e95618af4dcb0c56c29
  open_file_ios: 461db5853723763573e140de3193656f91990d9e
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: 4305caec6b40dde0ae97be1573c53de1882a07e5

COCOAPODS: 1.16.2
