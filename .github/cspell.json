{"version": "0.2", "$schema": "https://raw.githubusercontent.com/streetsidesoftware/cspell/main/cspell.schema.json", "dictionaries": ["vgv_allowed", "vgv_forbidden"], "dictionaryDefinitions": [{"name": "vgv_allowed", "path": "https://raw.githubusercontent.com/verygoodopensource/very_good_dictionaries/main/allowed.txt", "description": "Allowed VGV Spellings"}, {"name": "vgv_forbidden", "path": "https://raw.githubusercontent.com/verygoodopensource/very_good_dictionaries/main/forbidden.txt", "description": "Forbidden VGV Spellings"}], "useGitignore": true, "words": ["<PERSON><PERSON><PERSON>", "localizable", "mostrado", "p<PERSON><PERSON><PERSON>", "Texto", "Architecting", "architecting", "Liskov", "l<PERSON>ov", "<PERSON><PERSON>", "iskov", "Interface", "interface", "Nterface", "nterface", "Dependency", "dependency", "Ependency", "ependency", "Clarity", "clarity", "<PERSON><PERSON>", "larity", "Reliability", "reliability", "Eliability", "eliability", "Efficiency", "efficiency", "Fficiency", "fficiency", "Durability", "durability", "Urability", "urability", "glassmorphic", "Glassmorphic", "hotfix", "hotfixes", "Hotfixes", "Getx", "GetX", "Namaste", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "snackbars", "RPATH", "MULTICONFIG", "DUNICODE", "endfunction", "icudtl", "NOMINMAX", "fintech", "Codemagic", "webp", "WCAG", "geospatial", "telemedicine", "zuelligpharma", "ezhealth", "eztrade", "Phatek", "ABEYAANTRIX", "CBNITS", "Z<PERSON>lig", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Noida", "ICSE", "criss", "POPCTA", "<PERSON><PERSON>"]}