// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in my_portfolio_web/test/app/controllers/analytics_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:firebase_analytics/firebase_analytics.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:my_portfolio_web/app/services/analytics_service.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFirebaseAnalyticsObserver_0 extends _i1.SmartFake
    implements _i2.FirebaseAnalyticsObserver {
  _FakeFirebaseAnalyticsObserver_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [AnalyticsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAnalyticsService extends _i1.Mock implements _i3.AnalyticsService {
  MockAnalyticsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FirebaseAnalyticsObserver get observer => (super.noSuchMethod(
        Invocation.getter(#observer),
        returnValue: _FakeFirebaseAnalyticsObserver_0(
          this,
          Invocation.getter(#observer),
        ),
      ) as _i2.FirebaseAnalyticsObserver);

  @override
  bool get isInitialized => (super.noSuchMethod(
        Invocation.getter(#isInitialized),
        returnValue: false,
      ) as bool);

  @override
  List<Map<String, dynamic>> get debugEvents => (super.noSuchMethod(
        Invocation.getter(#debugEvents),
        returnValue: <Map<String, dynamic>>[],
      ) as List<Map<String, dynamic>>);

  @override
  _i4.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> trackPageView({
    required String? pageName,
    String? pageClass,
    Map<String, Object>? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #trackPageView,
          [],
          {
            #pageName: pageName,
            #pageClass: pageClass,
            #parameters: parameters,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> trackButtonClick({
    required String? buttonName,
    String? section,
    Map<String, Object>? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #trackButtonClick,
          [],
          {
            #buttonName: buttonName,
            #section: section,
            #parameters: parameters,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> trackProjectInteraction({
    required String? projectName,
    required String? action,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #trackProjectInteraction,
          [],
          {
            #projectName: projectName,
            #action: action,
            #parameters: parameters,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> trackContactInteraction({
    required String? action,
    String? method,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #trackContactInteraction,
          [],
          {
            #action: action,
            #method: method,
            #parameters: parameters,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> trackSkillInteraction({
    required String? skillName,
    required String? action,
    String? category,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #trackSkillInteraction,
          [],
          {
            #skillName: skillName,
            #action: action,
            #category: category,
            #parameters: parameters,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> trackCVDownload({
    String? source,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #trackCVDownload,
          [],
          {
            #source: source,
            #parameters: parameters,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> trackSocialMediaClick({
    required String? platform,
    String? source,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #trackSocialMediaClick,
          [],
          {
            #platform: platform,
            #source: source,
            #parameters: parameters,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> trackCustomEvent({
    required String? eventName,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #trackCustomEvent,
          [],
          {
            #eventName: eventName,
            #parameters: parameters,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setUserId(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #setUserId,
          [userId],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> resetAnalyticsData() => (super.noSuchMethod(
        Invocation.method(
          #resetAnalyticsData,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  void clearDebugEvents() => super.noSuchMethod(
        Invocation.method(
          #clearDebugEvents,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void printDebugEvents() => super.noSuchMethod(
        Invocation.method(
          #printDebugEvents,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  Map<String, dynamic> getAnalyticsStatus() => (super.noSuchMethod(
        Invocation.method(
          #getAnalyticsStatus,
          [],
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);
}
